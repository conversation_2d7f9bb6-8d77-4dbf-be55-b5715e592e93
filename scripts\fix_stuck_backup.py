#!/usr/bin/env python3
"""
Comprehensive solution for stuck backup process.

This script:
1. Kills any stuck backup processes
2. Cleans up temporary files
3. Provides options to resume backup from where it left off
4. Implements improved hanging detection
"""

import os
import sys
import time
import json
import psutil
import logging
import subprocess
from pathlib import Path
from datetime import datetime

def setup_logging():
    """Setup logging for the fix script."""
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    log_file = f"fix_stuck_backup_{timestamp}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(log_file)
        ]
    )
    return logging.getLogger(__name__)

def find_backup_processes():
    """Find all backup-related Python processes."""
    backup_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time']):
        try:
            if proc.info['name'] and 'python' in proc.info['name'].lower():
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                if any(keyword in cmdline.lower() for keyword in [
                    'tngd_backup', 'run_backup', 'backup_engine', 'devo_client'
                ]):
                    backup_processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'cmdline': cmdline,
                        'create_time': proc.info['create_time'],
                        'process': proc
                    })
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            continue
    
    return backup_processes

def kill_backup_processes(logger):
    """Kill all backup processes."""
    backup_processes = find_backup_processes()
    
    if not backup_processes:
        logger.info("No backup processes found running")
        return True
    
    logger.info(f"Found {len(backup_processes)} backup-related processes")
    for proc in backup_processes:
        runtime = time.time() - proc['create_time']
        logger.info(f"  PID {proc['pid']}: {proc['name']} (running for {runtime/3600:.1f} hours)")
    
    # Kill processes
    killed_count = 0
    for proc in backup_processes:
        try:
            logger.info(f"Killing process PID {proc['pid']}")
            proc['process'].terminate()
            proc['process'].wait(timeout=5)
            killed_count += 1
        except psutil.TimeoutExpired:
            try:
                logger.warning(f"Force killing process PID {proc['pid']}")
                proc['process'].kill()
                killed_count += 1
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass
    
    logger.info(f"Successfully killed {killed_count}/{len(backup_processes)} processes")
    return True

def cleanup_temp_files(logger):
    """Clean up temporary files and directories."""
    temp_dirs = [
        Path("data/exports"),
        Path("data/temp"),
        Path("temp"),
        Path("tmp")
    ]
    
    cleaned_files = 0
    freed_space = 0
    
    for temp_dir in temp_dirs:
        if temp_dir.exists():
            logger.info(f"Cleaning up temporary directory: {temp_dir}")
            try:
                for file_path in temp_dir.rglob("*"):
                    if file_path.is_file() and file_path.suffix in ['.json', '.tmp', '.temp']:
                        try:
                            file_size = file_path.stat().st_size
                            file_path.unlink()
                            cleaned_files += 1
                            freed_space += file_size
                        except (OSError, PermissionError) as e:
                            logger.warning(f"Could not delete {file_path}: {e}")
            except Exception as e:
                logger.error(f"Error cleaning {temp_dir}: {e}")
    
    logger.info(f"Cleanup complete: {cleaned_files} files deleted, {freed_space / (1024*1024):.2f} MB freed")

def analyze_backup_progress(logger):
    """Analyze backup progress from logs."""
    log_dir = Path("data/logs")
    if not log_dir.exists():
        logger.warning("No log directory found")
        return None, []
    
    # Find the most recent log file
    log_files = list(log_dir.glob("*.log"))
    if not log_files:
        logger.warning("No log files found")
        return None, []
    
    latest_log = max(log_files, key=lambda x: x.stat().st_mtime)
    logger.info(f"Analyzing log file: {latest_log}")
    
    completed_tables = set()
    last_table = None
    
    try:
        with open(latest_log, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for line in lines:
            # Look for completed tables
            if "[COMPLETE] Table completed:" in line:
                try:
                    table_part = line.split("[COMPLETE] Table completed:")[1].strip()
                    table_name = table_part.split("(")[0].strip()
                    completed_tables.add(table_name)
                except (IndexError, AttributeError):
                    continue
            
            # Look for last processed table
            if "[PROCESS] Processing table" in line:
                try:
                    table_part = line.split(": ")[1].strip()
                    last_table = table_part
                except (IndexError, AttributeError):
                    continue
        
        logger.info(f"Found {len(completed_tables)} completed tables")
        if last_table:
            logger.info(f"Last processed table: {last_table}")
        
        return last_table, list(completed_tables)
        
    except Exception as e:
        logger.error(f"Error analyzing log file: {e}")
        return None, []

def create_resume_config(completed_tables, logger):
    """Create resume configuration file."""
    # Load original tables
    tables_file = Path("config/tables_production.json")
    if not tables_file.exists():
        tables_file = Path("config/tables.json")
    
    if not tables_file.exists():
        logger.error("No tables configuration file found")
        return False
    
    with open(tables_file, 'r') as f:
        all_tables = json.load(f)
    
    # Create remaining tables list
    remaining_tables = [table for table in all_tables if table not in completed_tables]
    
    # Save resume configuration
    resume_file = Path("config/tables_resume.json")
    with open(resume_file, 'w') as f:
        json.dump(remaining_tables, f, indent=2)
    
    logger.info(f"Created resume configuration: {resume_file}")
    logger.info(f"Remaining tables to process: {len(remaining_tables)}")
    
    return True

def main():
    """Main function."""
    logger = setup_logging()
    logger.info("=== STUCK BACKUP PROCESS FIX ===")
    
    print("This script will:")
    print("1. Kill any stuck backup processes")
    print("2. Clean up temporary files")
    print("3. Analyze backup progress")
    print("4. Create resume configuration")
    print("5. Provide restart instructions")
    print()
    
    # Step 1: Kill stuck processes
    logger.info("Step 1: Killing stuck backup processes...")
    kill_backup_processes(logger)
    
    # Step 2: Clean up temp files
    logger.info("Step 2: Cleaning up temporary files...")
    cleanup_temp_files(logger)
    
    # Step 3: Analyze progress
    logger.info("Step 3: Analyzing backup progress...")
    last_table, completed_tables = analyze_backup_progress(logger)
    
    # Step 4: Create resume config
    if completed_tables:
        logger.info("Step 4: Creating resume configuration...")
        create_resume_config(completed_tables, logger)
        
        print("\n" + "="*60)
        print("BACKUP ANALYSIS COMPLETE")
        print("="*60)
        print(f"Completed tables: {len(completed_tables)}")
        if last_table:
            print(f"Last processed: {last_table}")
        print("\nTo resume backup, run:")
        print("python run_backup.py 2025-04-15 --production --resume")
        print("="*60)
    else:
        print("\n" + "="*60)
        print("NO PROGRESS DETECTED")
        print("="*60)
        print("Starting fresh backup...")
        print("python run_backup.py 2025-04-15 --production")
        print("="*60)
    
    logger.info("Fix complete!")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)
