#!/usr/bin/env python3
"""
OSS Upload Verification Script

This script verifies that backup files are being successfully uploaded to OSS.
It checks recent uploads and provides detailed information about the backup files.

Usage:
    python verify_oss_uploads.py [date]
    
Examples:
    python verify_oss_uploads.py                    # Check today's uploads
    python verify_oss_uploads.py 2025-04-15        # Check specific date
"""

import sys
import os
from datetime import datetime, timedelta
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from tngd_backup.core.storage_manager import StorageManager
from tngd_backup.core.config_manager import ConfigManager


def format_size(size_bytes):
    """Format file size in human readable format."""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.2f} {size_names[i]}"


def check_oss_uploads(target_date=None):
    """
    Check OSS uploads for a specific date.
    
    Args:
        target_date: Date to check (YYYY-MM-DD format), defaults to today
    """
    print("=" * 60)
    print("OSS Upload Verification Tool")
    print("=" * 60)
    
    # Initialize configuration and storage manager
    try:
        config = ConfigManager()
        storage = StorageManager(config)
        print("✅ Configuration loaded successfully")
    except Exception as e:
        print(f"❌ Failed to initialize: {e}")
        return False
    
    # Test OSS connection
    print("\n1. Testing OSS Connection...")
    success, message = storage.test_connection()
    print(f"   Connection: {'✅ SUCCESS' if success else '❌ FAILED'} - {message}")
    
    if not success:
        print("   Cannot proceed without OSS connection.")
        return False
    
    # Determine target date
    if target_date:
        try:
            check_date = datetime.strptime(target_date, '%Y-%m-%d')
        except ValueError:
            print(f"❌ Invalid date format: {target_date}. Use YYYY-MM-DD format.")
            return False
    else:
        check_date = datetime.now()
    
    date_str = check_date.strftime('%Y-%m-%d')
    print(f"\n2. Checking uploads for date: {date_str}")
    
    # Build OSS path prefix for the date
    # Based on your screenshot: Devo/April/week 3/2025-04-15/
    month_name = check_date.strftime('%B')
    week_num = (check_date.day - 1) // 7 + 1
    oss_prefix = f"Devo/{month_name}/week {week_num}/{date_str}/"
    
    print(f"   OSS Path: {oss_prefix}")
    
    # List objects in OSS
    print("\n3. Listing backup files...")
    try:
        objects = storage.list_oss_objects(oss_prefix)
        
        if not objects:
            print(f"   ⚠️ No backup files found for {date_str}")
            print(f"   Checked path: {oss_prefix}")
            return False
        
        print(f"   ✅ Found {len(objects)} backup files:")
        
        total_size = 0
        for obj_key in objects:
            # Get detailed info for each object
            obj_info = storage.get_object_info(obj_key)
            if obj_info:
                file_size = obj_info['size']
                total_size += file_size
                last_modified = obj_info['last_modified']
                
                # Extract table name from filename
                filename = Path(obj_key).name
                table_name = filename.replace('.tar.gz', '').replace(f'_{date_str}', '')
                
                print(f"   📁 {table_name}")
                print(f"      File: {filename}")
                print(f"      Size: {format_size(file_size)}")
                print(f"      Modified: {last_modified}")
                print(f"      OSS Path: {obj_key}")
                print()
            else:
                print(f"   ⚠️ Could not get info for: {obj_key}")
        
        print(f"📊 Summary:")
        print(f"   Total Files: {len(objects)}")
        print(f"   Total Size: {format_size(total_size)}")
        print(f"   Date: {date_str}")
        print(f"   OSS Path: {oss_prefix}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error listing OSS objects: {e}")
        return False


def main():
    """Main entry point."""
    target_date = None
    
    if len(sys.argv) > 1:
        target_date = sys.argv[1]
    
    try:
        success = check_oss_uploads(target_date)
        
        if success:
            print("\n✅ OSS upload verification completed successfully!")
            return 0
        else:
            print("\n❌ OSS upload verification failed!")
            return 1
            
    except KeyboardInterrupt:
        print("\n⚠️ Verification cancelled by user")
        return 1
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
