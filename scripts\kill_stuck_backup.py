#!/usr/bin/env python3
"""
Emergency script to kill stuck backup processes and clean up resources.
"""

import os
import sys
import time
import psutil
import logging
from pathlib import Path

def setup_logging():
    """Setup logging for the kill script."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('kill_stuck_backup.log')
        ]
    )
    return logging.getLogger(__name__)

def find_backup_processes():
    """Find all backup-related Python processes."""
    backup_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time']):
        try:
            if proc.info['name'] and 'python' in proc.info['name'].lower():
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                if any(keyword in cmdline.lower() for keyword in [
                    'tngd_backup', 'run_backup', 'backup_engine', 'devo_client'
                ]):
                    backup_processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'cmdline': cmdline,
                        'create_time': proc.info['create_time'],
                        'process': proc
                    })
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            continue
    
    return backup_processes

def kill_process_tree(pid, logger):
    """Kill a process and all its children."""
    try:
        parent = psutil.Process(pid)
        children = parent.children(recursive=True)
        
        # Kill children first
        for child in children:
            try:
                logger.info(f"Killing child process: PID {child.pid}")
                child.terminate()
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        # Wait for children to terminate
        gone, alive = psutil.wait_procs(children, timeout=5)
        
        # Force kill any remaining children
        for child in alive:
            try:
                logger.warning(f"Force killing child process: PID {child.pid}")
                child.kill()
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        # Kill parent
        try:
            logger.info(f"Killing parent process: PID {pid}")
            parent.terminate()
            parent.wait(timeout=5)
        except psutil.TimeoutExpired:
            logger.warning(f"Force killing parent process: PID {pid}")
            parent.kill()
            
        return True
        
    except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
        logger.error(f"Failed to kill process {pid}: {e}")
        return False

def cleanup_temp_files(logger):
    """Clean up temporary files and directories."""
    temp_dirs = [
        Path("data/exports"),
        Path("data/temp"),
        Path("temp"),
        Path("tmp")
    ]
    
    cleaned_files = 0
    freed_space = 0
    
    for temp_dir in temp_dirs:
        if temp_dir.exists():
            logger.info(f"Cleaning up temporary directory: {temp_dir}")
            try:
                for file_path in temp_dir.rglob("*"):
                    if file_path.is_file():
                        try:
                            file_size = file_path.stat().st_size
                            file_path.unlink()
                            cleaned_files += 1
                            freed_space += file_size
                            logger.debug(f"Deleted: {file_path}")
                        except (OSError, PermissionError) as e:
                            logger.warning(f"Could not delete {file_path}: {e}")
            except Exception as e:
                logger.error(f"Error cleaning {temp_dir}: {e}")
    
    logger.info(f"Cleanup complete: {cleaned_files} files deleted, {freed_space / (1024*1024):.2f} MB freed")

def main():
    """Main function to kill stuck processes and clean up."""
    logger = setup_logging()
    logger.info("=== EMERGENCY BACKUP PROCESS KILLER ===")
    
    # Find backup processes
    backup_processes = find_backup_processes()
    
    if not backup_processes:
        logger.info("No backup processes found running")
        return
    
    logger.info(f"Found {len(backup_processes)} backup-related processes:")
    for proc in backup_processes:
        runtime = time.time() - proc['create_time']
        logger.info(f"  PID {proc['pid']}: {proc['name']} (running for {runtime/3600:.1f} hours)")
        logger.info(f"    Command: {proc['cmdline'][:100]}...")
    
    # Ask for confirmation
    response = input("\nDo you want to kill these processes? (y/N): ").strip().lower()
    if response != 'y':
        logger.info("Operation cancelled by user")
        return
    
    # Kill processes
    killed_count = 0
    for proc in backup_processes:
        logger.info(f"Killing process PID {proc['pid']}")
        if kill_process_tree(proc['pid'], logger):
            killed_count += 1
        else:
            logger.error(f"Failed to kill process PID {proc['pid']}")
    
    logger.info(f"Successfully killed {killed_count}/{len(backup_processes)} processes")
    
    # Clean up temporary files
    cleanup_response = input("\nDo you want to clean up temporary files? (y/N): ").strip().lower()
    if cleanup_response == 'y':
        cleanup_temp_files(logger)
    
    logger.info("=== CLEANUP COMPLETE ===")
    logger.info("You can now restart the backup process")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)
