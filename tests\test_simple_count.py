#!/usr/bin/env python3
"""
Simple test for the row count function.
"""

import os
import sys
import logging

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from tngd_backup.core.devo_client import DevoClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_simple_count():
    """Test just the row count function."""
    
    print("🔍 Testing Simple Row Count Function")
    print("=" * 50)
    
    # Test table and date
    test_table = 'my.app.tngd.actiontraillinux'
    where_clause = f"eventdate >= 1744934400000 and eventdate <= 1745020799999"
    
    try:
        # Initialize Devo client
        devo_client = DevoClient()
        
        print(f"📊 Testing table: {test_table}")
        print(f"🔍 WHERE clause: {where_clause}")
        print()
        
        # Test the count function
        print("🔍 Getting row count...")
        total_count = devo_client.get_table_row_count(test_table, where_clause, timeout=600)
        print(f"   Result: {total_count:,} rows")
        
        if total_count > 0:
            print(f"   ✅ Row count function works!")
            print(f"   📊 This means your email reports can show: {total_count:,} rows")
            print(f"   📝 Instead of the current 50,000 row limitation")
        else:
            print(f"   ❌ Row count function returned 0 - needs debugging")
        
        return total_count
        
    except Exception as e:
        logger.error(f"Error in simple count test: {str(e)}")
        return 0

if __name__ == "__main__":
    test_simple_count()
