#!/usr/bin/env python3
"""
Test script to verify time-based chunking approach for getting ALL data.
This bypasses Devo OFFSET limitations by splitting the day into time periods.
"""

import os
import sys
import logging
import tempfile
from datetime import datetime

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from tngd_backup.core.devo_client import DevoClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_time_chunking():
    """Test time-based chunking vs standard OFFSET approach."""
    
    print("🕒 Testing Time-Based Chunking for Complete Data Retrieval")
    print("=" * 70)
    
    # Test parameters
    test_table = 'cloud.office365.management.exchange'  # Known to have 50k+ rows
    test_date = '2025-04-18'
    
    try:
        # Initialize Devo client
        devo_client = DevoClient()
        
        print(f"📊 Testing table: {test_table}")
        print(f"📅 Testing date: {test_date}")
        print()
        
        # Create temporary directories for comparison
        with tempfile.TemporaryDirectory() as temp_dir:
            offset_dir = os.path.join(temp_dir, "offset_method")
            time_dir = os.path.join(temp_dir, "time_method")
            os.makedirs(offset_dir, exist_ok=True)
            os.makedirs(time_dir, exist_ok=True)
            
            # Test 1: Standard OFFSET method (current approach)
            print("🔍 Test 1: Standard OFFSET Method (Current)")
            print("-" * 50)
            
            from tngd_backup.utils.timestamp_helper import create_date_filter
            where_clause = create_date_filter(test_date, simple=True)
            
            offset_result = devo_client.query_table_to_file(
                table_name=test_table,
                where_clause=where_clause,
                output_dir=offset_dir,
                chunk_size=50000,
                timeout=600
            )
            
            offset_rows = offset_result.get('total_rows', 0)
            offset_chunks = offset_result.get('chunk_count', 0)
            
            print(f"   📊 OFFSET Method Results:")
            print(f"      - Total rows: {offset_rows:,}")
            print(f"      - Chunks: {offset_chunks}")
            print(f"      - Status: {offset_result.get('status', 'unknown')}")
            print()
            
            # Test 2: Time-based chunking method (new approach)
            print("🕒 Test 2: Time-Based Chunking Method (New)")
            print("-" * 50)
            
            time_result = devo_client.query_table_with_time_chunking(
                table_name=test_table,
                date_str=test_date,
                output_dir=time_dir,
                chunk_size=50000,
                timeout=600
            )
            
            time_rows = time_result.get('total_rows', 0)
            time_chunks = time_result.get('chunk_count', 0)
            
            print(f"   📊 Time Chunking Results:")
            print(f"      - Total rows: {time_rows:,}")
            print(f"      - Chunks: {time_chunks}")
            print(f"      - Status: {time_result.get('status', 'unknown')}")
            print()
            
            # Test 3: Comparison and Analysis
            print("📈 Analysis & Comparison")
            print("-" * 50)
            
            if time_rows > offset_rows:
                improvement = time_rows - offset_rows
                percentage = (improvement / offset_rows * 100) if offset_rows > 0 else 0
                
                print(f"   ✅ SUCCESS! Time chunking retrieved MORE data:")
                print(f"      - Additional rows: {improvement:,}")
                print(f"      - Improvement: {percentage:.1f}%")
                print(f"      - OFFSET method: {offset_rows:,} rows")
                print(f"      - Time method: {time_rows:,} rows")
                print()
                print(f"   🎯 AUDIT IMPACT:")
                print(f"      - You now have {improvement:,} more rows for audit")
                print(f"      - This represents complete data vs partial data")
                print(f"      - Time chunking bypassed Devo OFFSET limitations")
                
            elif time_rows == offset_rows:
                print(f"   📊 Same row count: {time_rows:,}")
                print(f"      - Both methods retrieved the same amount of data")
                print(f"      - This table may not have OFFSET limitations")
                print(f"      - Or the table actually has exactly {time_rows:,} rows")
                
            else:
                print(f"   ⚠️  Time chunking got fewer rows:")
                print(f"      - OFFSET method: {offset_rows:,} rows")
                print(f"      - Time method: {time_rows:,} rows")
                print(f"      - This needs investigation")
            
            print()
            print("🎯 RECOMMENDATION FOR AUDIT:")
            if time_rows > offset_rows:
                print(f"   ✅ Use time-based chunking for complete audit data")
                print(f"   ✅ This ensures you get ALL rows, not just first 50,000")
                print(f"   ✅ Critical for audit compliance and data completeness")
            else:
                print(f"   📝 Current OFFSET method appears sufficient for this table")
                print(f"   📝 But time chunking provides better guarantee of completeness")
            
            return {
                'offset_rows': offset_rows,
                'time_rows': time_rows,
                'improvement': time_rows - offset_rows,
                'success': time_rows >= offset_rows
            }
        
    except Exception as e:
        logger.error(f"Error in time chunking test: {str(e)}")
        return None

if __name__ == "__main__":
    test_time_chunking()
