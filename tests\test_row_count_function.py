#!/usr/bin/env python3
"""
Test script to verify the new row count function approach.
"""

import os
import sys
import logging
from datetime import datetime

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from tngd_backup.core.devo_client import DevoClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_row_count_function():
    """Test the new get_table_row_count function."""
    
    print("🔍 Testing New Row Count Function Approach")
    print("=" * 60)
    
    # Test table and date
    test_table = 'my.app.tngd.actiontraillinux'
    test_date = '2025-04-18'
    where_clause = f"eventdate >= 1744934400000 and eventdate <= 1745020799999"
    
    try:
        # Initialize Devo client
        devo_client = DevoClient()
        
        print(f"📊 Testing table: {test_table}")
        print(f"📅 Testing date: {test_date}")
        print(f"🔍 WHERE clause: {where_clause}")
        print()
        
        # Test 1: Get row count using new function
        print("🔍 Test 1: Using new get_table_row_count function")
        total_count = devo_client.get_table_row_count(test_table, where_clause)
        print(f"   Total row count: {total_count:,}")
        print()
        
        # Test 2: Compare with chunked retrieval
        print("🔍 Test 2: Compare with chunked data retrieval")
        
        # Create temp directory
        import tempfile
        with tempfile.TemporaryDirectory() as temp_dir:
            result = devo_client.query_table_to_file(
                table_name=test_table,
                where_clause=where_clause,
                output_dir=temp_dir,
                chunk_size=50000,
                timeout=600
            )
            
            print(f"   Query result:")
            print(f"     - Status: {result.get('status')}")
            print(f"     - Total rows (reported): {result.get('total_rows', 0):,}")
            print(f"     - Actual total rows: {result.get('actual_total_rows', 0):,}")
            print(f"     - Retrieved rows: {result.get('retrieved_rows', 0):,}")
            print(f"     - Is complete: {result.get('is_complete', False)}")
            print(f"     - Chunk count: {result.get('chunk_count', 0)}")
            print()
        
        # Test 3: Analysis
        print("📊 Analysis:")
        reported_total = result.get('total_rows', 0)
        actual_total = result.get('actual_total_rows', 0)
        retrieved = result.get('retrieved_rows', 0)
        is_complete = result.get('is_complete', False)
        
        if actual_total > 0:
            print(f"   ✅ Row count function works: {actual_total:,} total rows")
            
            if is_complete:
                print(f"   ✅ Complete data retrieved: {retrieved:,} rows")
            else:
                percentage = (retrieved / actual_total * 100) if actual_total > 0 else 0
                print(f"   ⚠️  Incomplete data: {retrieved:,} of {actual_total:,} rows ({percentage:.1f}%)")
                print(f"   📝 This is due to Devo platform OFFSET limitations")
            
            if reported_total == actual_total:
                print(f"   ✅ Email reports will show accurate count: {reported_total:,}")
            else:
                print(f"   ✅ Email reports will show actual total: {reported_total:,} (was {retrieved:,})")
        else:
            print(f"   ❌ Row count function failed")
        
        print()
        print("🎯 CONCLUSION:")
        if actual_total > 0:
            print(f"   ✅ New approach works! Email reports will show {reported_total:,} rows")
            print(f"   ✅ This represents the actual total data available")
            if not is_complete:
                print(f"   📝 Note: Only {retrieved:,} rows were retrievable due to Devo limitations")
        else:
            print(f"   ❌ New approach needs debugging")
        
        return {
            'total_count_function': total_count,
            'reported_total': reported_total,
            'actual_total': actual_total,
            'retrieved': retrieved,
            'is_complete': is_complete
        }
        
    except Exception as e:
        logger.error(f"Error in row count function test: {str(e)}")
        return None

if __name__ == "__main__":
    test_row_count_function()
