#!/usr/bin/env python3
"""
Test script to verify Devo query limits and offset behavior.
"""

import os
import sys
import logging
from datetime import datetime

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from tngd_backup.core.devo_client import DevoClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_devo_query_limits():
    """Test Devo query limits and offset behavior."""
    
    print("🔍 Testing Devo Query Limits and Offset Behavior")
    print("=" * 60)
    
    # Test table and date
    test_table = 'my.app.tngd.actiontraillinux'
    test_date = '2025-04-18'
    
    try:
        # Initialize Devo client
        devo_client = DevoClient()
        
        print(f"📊 Testing table: {test_table}")
        print(f"📅 Testing date: {test_date}")
        print()
        
        # Test 1: Count query to get actual total
        print("🔍 Test 1: Getting actual row count")
        count_query = f"from {test_table} where eventdate >= 1744934400000 and eventdate <= 1745020799999 select count() as total_count"
        count_result = devo_client.execute_query(count_query, timeout=300)
        
        if count_result and len(count_result) > 0:
            actual_total = count_result[0].get('total_count', 0)
            print(f"   Actual total rows: {actual_total:,}")
        else:
            actual_total = 0
            print(f"   Count query failed or returned no results")
        
        # Test 2: Query with different limits
        print(f"\n🔍 Test 2: Testing different query limits")
        
        limits_to_test = [1000, 10000, 50000, 100000]
        
        for limit in limits_to_test:
            try:
                query = f"from {test_table} where eventdate >= 1744934400000 and eventdate <= 1745020799999 select * limit {limit}"
                result = devo_client.execute_query(query, timeout=300)
                rows_returned = len(result) if result else 0
                print(f"   Limit {limit:,}: {rows_returned:,} rows returned")
                
                if rows_returned < limit and rows_returned > 0:
                    print(f"      → This appears to be the actual data limit")
                elif rows_returned == limit:
                    print(f"      → Query limit reached, may have more data")
                
            except Exception as e:
                print(f"   Limit {limit:,}: ERROR - {str(e)}")
        
        # Test 3: Test offset behavior
        print(f"\n🔍 Test 3: Testing offset behavior")
        
        offsets_to_test = [0, 25000, 50000, 75000, 100000]
        
        for offset in offsets_to_test:
            try:
                query = f"from {test_table} where eventdate >= 1744934400000 and eventdate <= 1745020799999 select * limit 10000 offset {offset}"
                result = devo_client.execute_query(query, timeout=300)
                rows_returned = len(result) if result else 0
                print(f"   Offset {offset:,}: {rows_returned:,} rows returned")
                
                if rows_returned == 0 and offset > 0:
                    print(f"      → No more data beyond offset {offset:,}")
                    break
                
            except Exception as e:
                print(f"   Offset {offset:,}: ERROR - {str(e)}")
        
        # Test 4: Test chunked queries like the backup system
        print(f"\n🔍 Test 4: Testing chunked queries (backup system simulation)")
        
        chunk_size = 50000
        total_rows_found = 0
        chunk_num = 1
        
        while True:
            offset = (chunk_num - 1) * chunk_size
            
            try:
                query = f"from {test_table} where eventdate >= 1744934400000 and eventdate <= 1745020799999 select * limit {chunk_size} offset {offset}"
                result = devo_client.execute_query(query, timeout=300)
                rows_returned = len(result) if result else 0
                
                print(f"   Chunk {chunk_num} (offset {offset:,}): {rows_returned:,} rows")
                
                if rows_returned == 0:
                    print(f"      → No more data, stopping")
                    break
                
                total_rows_found += rows_returned
                chunk_num += 1
                
                # Safety limit
                if chunk_num > 10:
                    print(f"      → Safety limit reached, stopping")
                    break
                
            except Exception as e:
                print(f"   Chunk {chunk_num}: ERROR - {str(e)}")
                break
        
        print(f"\n📊 SUMMARY:")
        print(f"   Count query result: {actual_total:,} rows")
        print(f"   Chunked query total: {total_rows_found:,} rows")
        
        if actual_total == total_rows_found:
            print(f"   ✅ Row counts match - system is working correctly")
        elif total_rows_found == 50000:
            print(f"   🚨 ISSUE: Only got 50,000 rows, but count shows {actual_total:,}")
            print(f"   This suggests a Devo query result limit of 50,000 rows")
        else:
            print(f"   ⚠️  Row count mismatch needs investigation")
        
        return {
            'actual_total': actual_total,
            'chunked_total': total_rows_found,
            'chunks_processed': chunk_num - 1
        }
        
    except Exception as e:
        logger.error(f"Error in Devo limits test: {str(e)}")
        return None

if __name__ == "__main__":
    test_devo_query_limits()
