#!/usr/bin/env python3
"""
Test script to verify the chunking fix works correctly.
This script tests the _fix_query_syntax method to ensure it preserves LIMIT and OFFSET clauses.
"""

import sys
import os
import re

# Add the src directory to the path so we can import the modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from tngd_backup.core.devo_client import DevoClient

def test_fix_query_syntax():
    """Test the _fix_query_syntax method with various query patterns."""
    
    # Create a DevoClient instance (we don't need actual credentials for this test)
    client = DevoClient()
    
    test_cases = [
        {
            "name": "Query with eventdate WHERE and LIMIT/OFFSET",
            "input": "from my.app.tngd.actiontraillinux select * where eventdate >= 1744675200000 and eventdate <= 1744761599999 limit 50000 offset 0",
            "expected_contains": ["limit 50000", "offset 0"],
            "expected_not_contains": ["where", "eventdate"]
        },
        {
            "name": "Query with eventdate WHERE and LIMIT/OFFSET (different offset)",
            "input": "from my.app.tngd.actiontraillinux select * where eventdate >= 1744675200000 and eventdate <= 1744761599999 limit 50000 offset 100000",
            "expected_contains": ["limit 50000", "offset 100000"],
            "expected_not_contains": ["where", "eventdate"]
        },
        {
            "name": "Query with only LIMIT (no OFFSET)",
            "input": "from my.app.tngd.actiontraillinux select * where eventdate >= 1744675200000 and eventdate <= 1744761599999 limit 25000",
            "expected_contains": ["limit 25000"],
            "expected_not_contains": ["where", "eventdate", "offset"]
        },
        {
            "name": "Query without WHERE clause",
            "input": "from my.app.tngd.actiontraillinux select * limit 50000 offset 0",
            "expected_contains": ["limit 50000", "offset 0"],
            "expected_not_contains": ["where", "eventdate"]
        },
        {
            "name": "Query with non-eventdate WHERE clause",
            "input": "from my.app.tngd.actiontraillinux select * where userid = 'test' limit 50000 offset 0",
            "expected_contains": ["where userid = 'test'", "limit 50000", "offset 0"],
            "expected_not_contains": ["eventdate"]
        }
    ]
    
    print("Testing _fix_query_syntax method...")
    print("=" * 60)
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest {i}: {test_case['name']}")
        print(f"Input:  {test_case['input']}")
        
        try:
            result = client._fix_query_syntax(test_case['input'])
            print(f"Output: {result}")
            
            # Check expected_contains
            for expected in test_case['expected_contains']:
                if expected not in result:
                    print(f"❌ FAIL: Expected '{expected}' to be in result")
                    all_passed = False
                else:
                    print(f"✅ PASS: Found '{expected}' in result")
            
            # Check expected_not_contains
            for not_expected in test_case['expected_not_contains']:
                if not_expected in result:
                    print(f"❌ FAIL: Expected '{not_expected}' NOT to be in result")
                    all_passed = False
                else:
                    print(f"✅ PASS: '{not_expected}' correctly not in result")
                    
        except Exception as e:
            print(f"❌ ERROR: {str(e)}")
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 ALL TESTS PASSED! The chunking fix is working correctly.")
    else:
        print("❌ SOME TESTS FAILED! Please check the implementation.")
    
    return all_passed

def test_chunking_logic():
    """Test that different offsets would produce different queries."""
    
    print("\n\nTesting chunking logic...")
    print("=" * 60)
    
    client = DevoClient()
    
    # Simulate what happens in query_table_to_file
    table_name = "my.app.tngd.actiontraillinux"
    where_clause = "eventdate >= 1744675200000 and eventdate <= 1744761599999"
    chunk_size = 50000
    
    for chunk_num in range(3):
        offset = chunk_num * chunk_size
        
        # This is what execute_query_chunked would build
        base_query = f"from {table_name} select *"
        if where_clause:
            base_query += f" where {where_clause}"
        
        chunked_query = f"{base_query.rstrip()} limit {chunk_size} offset {offset}"
        
        print(f"\nChunk {chunk_num + 1} (offset={offset}):")
        print(f"Before fix: {chunked_query}")
        
        fixed_query = client._fix_query_syntax(chunked_query)
        print(f"After fix:  {fixed_query}")
        
        # Verify the offset is preserved and different for each chunk
        if f"offset {offset}" not in fixed_query:
            print(f"❌ FAIL: Offset {offset} not preserved!")
            return False
        else:
            print(f"✅ PASS: Offset {offset} correctly preserved")
    
    print("\n🎉 Chunking logic test passed!")
    return True

if __name__ == "__main__":
    print("TNGD Backup System - Chunking Fix Test")
    print("=" * 60)
    
    success1 = test_fix_query_syntax()
    success2 = test_chunking_logic()
    
    if success1 and success2:
        print("\n🎉 ALL TESTS PASSED! The fix should resolve the duplicate chunk issue.")
        sys.exit(0)
    else:
        print("\n❌ TESTS FAILED! Please review the implementation.")
        sys.exit(1)
