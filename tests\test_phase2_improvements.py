#!/usr/bin/env python3
"""
Test script for Phase 2 thread management improvements.

This script tests the Phase 2 architecture improvements:
- Single shared thread pool pattern
- Memory-based thread throttling
- Enhanced error handling and recovery
- Windows-specific optimizations
"""

import sys
import time
import threading
import logging
from concurrent.futures import as_completed

# Add the src directory to the path
sys.path.insert(0, 'src')

from tngd_backup.core.thread_manager import (
    get_thread_manager, managed_thread_pool, submit_shared_task,
    PoolStrategy, ThreadManager
)
from tngd_backup.constants import BackupConstants

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def memory_intensive_task(task_id: int, memory_mb: int = 10):
    """Simulate a memory-intensive task."""
    logger.info(f"Memory task {task_id} starting (allocating {memory_mb}MB)")
    
    # Allocate some memory
    data = bytearray(memory_mb * 1024 * 1024)  # Allocate memory
    time.sleep(1.0)  # Simulate work
    
    # Clear memory
    del data
    logger.info(f"Memory task {task_id} completed")
    return f"Memory task {task_id} result"


def cpu_intensive_task(task_id: int, duration: float = 1.0):
    """Simulate a CPU-intensive task."""
    logger.info(f"CPU task {task_id} starting (duration: {duration}s)")
    
    # Simulate CPU work
    start_time = time.time()
    while time.time() - start_time < duration:
        # Some CPU work
        sum(range(1000))
    
    logger.info(f"CPU task {task_id} completed")
    return f"CPU task {task_id} result"


def test_single_shared_pool():
    """Test single shared thread pool functionality."""
    logger.info("=== Testing Single Shared Thread Pool ===")
    
    # Get thread manager with single shared pool strategy
    manager = get_thread_manager()
    logger.info(f"Thread manager strategy: {manager.strategy.value}")
    
    # Get initial metrics
    initial_metrics = manager.get_metrics()
    logger.info(f"Initial metrics: active_threads={initial_metrics.active_threads}, "
               f"shared_pool_tasks={initial_metrics.shared_pool_tasks}")
    
    # Submit tasks using the shared pool
    futures = []
    for i in range(5):
        future = submit_shared_task(cpu_intensive_task, i, 0.5, pool_name="cpu-test")
        futures.append(future)
    
    # Check metrics while tasks are running
    time.sleep(0.2)  # Let tasks start
    running_metrics = manager.get_metrics()
    logger.info(f"Running metrics: active_threads={running_metrics.active_threads}, "
               f"shared_pool_tasks={running_metrics.shared_pool_tasks}")
    
    # Wait for completion
    for future in as_completed(futures, timeout=30):
        try:
            result = future.result()
            logger.debug(f"Task result: {result}")
        except Exception as e:
            logger.error(f"Task failed: {e}")
    
    # Final metrics
    time.sleep(1)  # Allow cleanup
    final_metrics = manager.get_metrics()
    logger.info(f"Final metrics: active_threads={final_metrics.active_threads}, "
               f"shared_pool_tasks={final_metrics.shared_pool_tasks}")
    
    return True


def test_memory_throttling():
    """Test memory-based thread throttling."""
    logger.info("=== Testing Memory-Based Throttling ===")
    
    manager = get_thread_manager()
    
    # Get initial memory metrics
    initial_metrics = manager.get_metrics()
    logger.info(f"Initial memory: {initial_metrics.memory_usage_mb:.1f}MB, "
               f"throttled: {initial_metrics.memory_throttled}")
    
    # Submit memory-intensive tasks
    futures = []
    for i in range(3):
        # Allocate 50MB per task to trigger throttling
        future = submit_shared_task(memory_intensive_task, i, 50, pool_name="memory-test")
        futures.append(future)
    
    # Check if throttling is triggered
    time.sleep(0.5)
    throttle_metrics = manager.get_metrics()
    logger.info(f"During memory tasks: {throttle_metrics.memory_usage_mb:.1f}MB, "
               f"throttled: {throttle_metrics.memory_throttled}")
    
    # Wait for completion
    for future in as_completed(futures, timeout=60):
        try:
            result = future.result()
            logger.debug(f"Memory task result: {result}")
        except Exception as e:
            logger.error(f"Memory task failed: {e}")
    
    # Final memory check
    time.sleep(2)  # Allow memory cleanup
    final_metrics = manager.get_metrics()
    logger.info(f"Final memory: {final_metrics.memory_usage_mb:.1f}MB, "
               f"throttled: {final_metrics.memory_throttled}")
    
    return True


def test_mixed_workload():
    """Test mixed workload with both legacy and shared pool usage."""
    logger.info("=== Testing Mixed Workload ===")
    
    manager = get_thread_manager()
    
    # Submit tasks using both methods
    shared_futures = []
    legacy_futures = []
    
    # Shared pool tasks
    for i in range(3):
        future = submit_shared_task(cpu_intensive_task, f"shared-{i}", 0.3, pool_name="shared-mixed")
        shared_futures.append(future)
    
    # Legacy pool tasks (using context manager)
    with managed_thread_pool("legacy-mixed", max_workers=2) as pool:
        for i in range(3):
            future = pool.submit(cpu_intensive_task, f"legacy-{i}", 0.3)
            legacy_futures.append(future)
    
    # Monitor metrics during execution
    time.sleep(0.2)
    mixed_metrics = manager.get_metrics()
    logger.info(f"Mixed workload metrics: active_threads={mixed_metrics.active_threads}, "
               f"active_pools={mixed_metrics.active_pools}, "
               f"shared_pool_tasks={mixed_metrics.shared_pool_tasks}")
    
    # Wait for all tasks
    all_futures = shared_futures + legacy_futures
    for future in as_completed(all_futures, timeout=30):
        try:
            result = future.result()
            logger.debug(f"Mixed task result: {result}")
        except Exception as e:
            logger.error(f"Mixed task failed: {e}")
    
    return True


def test_error_handling():
    """Test enhanced error handling and recovery."""
    logger.info("=== Testing Error Handling ===")
    
    def failing_task(task_id: int):
        logger.info(f"Failing task {task_id} starting")
        time.sleep(0.1)
        raise RuntimeError(f"Intentional failure in task {task_id}")
    
    manager = get_thread_manager()
    
    # Submit some failing tasks
    futures = []
    for i in range(3):
        future = submit_shared_task(failing_task, i, pool_name="error-test")
        futures.append(future)
    
    # Handle errors
    error_count = 0
    for future in as_completed(futures, timeout=30):
        try:
            result = future.result()
            logger.info(f"Unexpected success: {result}")
        except Exception as e:
            logger.info(f"Expected error caught: {e}")
            error_count += 1
    
    # Check that the shared pool is still functional
    recovery_future = submit_shared_task(cpu_intensive_task, "recovery", 0.2, pool_name="recovery-test")
    try:
        result = recovery_future.result(timeout=10)
        logger.info(f"Recovery task successful: {result}")
        return error_count == 3  # All tasks should have failed
    except Exception as e:
        logger.error(f"Recovery task failed: {e}")
        return False


def main():
    """Run all Phase 2 tests."""
    logger.info("Starting Phase 2 Thread Management Tests")
    logger.info("=" * 60)
    
    tests = [
        test_single_shared_pool,
        test_memory_throttling,
        test_mixed_workload,
        test_error_handling
    ]
    
    results = []
    for test_func in tests:
        try:
            result = test_func()
            results.append(result)
            logger.info(f"✓ {test_func.__name__}: {'PASSED' if result else 'FAILED'}")
        except Exception as e:
            logger.error(f"✗ {test_func.__name__}: ERROR - {e}")
            results.append(False)
        
        # Small delay between tests
        time.sleep(1)
    
    # Summary
    logger.info("=" * 60)
    passed = sum(results)
    total = len(results)
    logger.info(f"Phase 2 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All Phase 2 tests passed! Architecture improvements are working.")
    else:
        logger.warning(f"⚠️  {total - passed} tests failed. Review the logs above.")
    
    # Final cleanup
    thread_manager = get_thread_manager()
    final_metrics = thread_manager.get_metrics()
    logger.info(f"Final system state: {final_metrics}")
    thread_manager.shutdown()
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
