#!/usr/bin/env python3
"""
Test script to verify row count accuracy in backup reports.

This script helps identify if the backup system is reporting actual row counts
or chunk size estimates in email reports.
"""

import os
import sys
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from tngd_backup.core.devo_client import DevoClient
from tngd_backup.core.backup_engine import BackupEngine
from tngd_backup.constants import BackupConstants

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_table_row_counts():
    """Test actual row counts vs reported row counts for specific tables."""
    
    print("🔍 Row Count Verification Test")
    print("=" * 50)
    
    # Test tables from your email report
    test_tables = [
        'my.app.tngd.actiontraillinux',
        'my.app.tngd.waf',
        'cloud.office365.management.exchange',
        'cloud.alibaba.log_service.events',
        'cef0.zscaler.nssweblog',
        'cloud.office365.management.airinvestigation'
    ]
    
    # Test date (use the date from your report)
    test_date = '2025-04-16'
    
    try:
        # Initialize Devo client
        devo_client = DevoClient()
        
        print(f"📅 Testing date: {test_date}")
        print(f"📊 Testing {len(test_tables)} tables")
        print()
        
        results = []
        
        for table_name in test_tables:
            print(f"🔍 Testing table: {table_name}")
            
            try:
                # Method 1: Get count using count() query
                count_query = f"from {table_name} where eventdate = '{test_date}' select count() as total_count"
                count_result = devo_client.execute_query(count_query, days=1, timeout=300)
                
                if count_result and len(count_result) > 0:
                    count_method_rows = count_result[0].get('total_count', 0)
                else:
                    count_method_rows = 0
                
                # Method 2: Get actual data and count rows
                data_query = f"from {table_name} where eventdate = '{test_date}' select *"
                data_result = devo_client.execute_query(data_query, days=1, timeout=600)
                actual_data_rows = len(data_result) if data_result else 0
                
                # Method 3: Use the backup system's query_table_to_file method
                where_clause = f"eventdate = '{test_date}'"
                temp_dir = Path("temp_row_verification")
                temp_dir.mkdir(exist_ok=True)
                
                backup_result = devo_client.query_table_to_file(
                    table_name=table_name,
                    where_clause=where_clause,
                    output_dir=str(temp_dir),
                    chunk_size=BackupConstants.DEFAULT_CHUNK_SIZE,
                    timeout=600
                )
                
                backup_reported_rows = backup_result.get('total_rows', 0)
                actual_rows_processed = backup_result.get('actual_rows_processed', backup_reported_rows)
                
                # Store results
                result = {
                    'table_name': table_name,
                    'count_query_rows': count_method_rows,
                    'actual_data_rows': actual_data_rows,
                    'backup_reported_rows': backup_reported_rows,
                    'actual_rows_processed': actual_rows_processed,
                    'chunk_files': len(backup_result.get('file_paths', [])),
                    'status': backup_result.get('status', 'unknown')
                }
                results.append(result)
                
                # Analysis
                print(f"   Count query result: {count_method_rows:,} rows")
                print(f"   Actual data retrieved: {actual_data_rows:,} rows")
                print(f"   Backup system reported: {backup_reported_rows:,} rows")
                print(f"   Actual rows processed: {actual_rows_processed:,} rows")
                print(f"   Chunk files created: {len(backup_result.get('file_paths', []))}")
                
                # Check for discrepancies
                if count_method_rows != actual_data_rows:
                    print(f"   ⚠️  DISCREPANCY: Count query vs actual data")
                
                if backup_reported_rows == BackupConstants.DEFAULT_CHUNK_SIZE:
                    print(f"   🚨 ISSUE: Backup reported exactly {BackupConstants.DEFAULT_CHUNK_SIZE:,} rows (chunk size)")
                
                if backup_reported_rows != actual_data_rows and actual_data_rows > 0:
                    print(f"   ⚠️  DISCREPANCY: Backup reported vs actual data")
                
                print()
                
            except Exception as e:
                print(f"   ❌ Error testing {table_name}: {str(e)}")
                print()
                continue
        
        # Summary analysis
        print("📊 SUMMARY ANALYSIS")
        print("=" * 50)
        
        chunk_size_matches = 0
        discrepancies = 0
        
        for result in results:
            if result['backup_reported_rows'] == BackupConstants.DEFAULT_CHUNK_SIZE:
                chunk_size_matches += 1
            
            if (result['backup_reported_rows'] != result['actual_data_rows'] and 
                result['actual_data_rows'] > 0):
                discrepancies += 1
        
        print(f"Tables tested: {len(results)}")
        print(f"Tables reporting exactly {BackupConstants.DEFAULT_CHUNK_SIZE:,} rows: {chunk_size_matches}")
        print(f"Tables with discrepancies: {discrepancies}")
        
        if chunk_size_matches > 0:
            print(f"\n🚨 ISSUE CONFIRMED: {chunk_size_matches} tables are reporting the chunk size instead of actual row counts!")
        
        if discrepancies > 0:
            print(f"\n⚠️  WARNING: {discrepancies} tables have row count discrepancies")
        
        # Save detailed results
        results_file = f"row_count_verification_{test_date}.json"
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n📄 Detailed results saved to: {results_file}")
        
        # Cleanup
        import shutil
        if temp_dir.exists():
            shutil.rmtree(temp_dir)
        
        return results
        
    except Exception as e:
        logger.error(f"Error in row count verification: {str(e)}")
        return []

if __name__ == "__main__":
    test_table_row_counts()
